// Background script for ColorNinja extension

let eyedropperState = {
  active: false,
  tabId: null,
  popupClosed: false
};

// Listen for messages from popup and content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'startEyedropperFromPopup') {
    handleEyedropperStart(message.tabId);
    sendResponse({ success: true });
  } else if (message.action === 'eyedropperResult') {
    handleEyedropperResult(message.color);
    sendResponse({ success: true });
  } else if (message.action === 'eyedropperError') {
    handleEyedropperError(message.message);
    sendResponse({ success: true });
  } else if (message.action === 'eyedropperCancelled') {
    handleEyedropperCancelled();
    sendResponse({ success: true });
  }
  return true;
});

// Handle eyedropper start
async function handleEyedropperStart(tabId) {
  eyedropperState.active = true;
  eyedropperState.tabId = tabId;
  eyedropperState.popupClosed = true;

  // Send message to content script
  chrome.tabs.sendMessage(tabId, { action: 'startEyedropper' });
}

// Handle eyedropper result
function handleEyedropperResult(color) {
  // Store the result and reopen popup
  chrome.storage.local.set({
    lastPickedColor: color,
    eyedropperResult: 'success'
  }, () => {
    // Reopen popup to show result
    chrome.action.openPopup();
  });

  eyedropperState.active = false;
  eyedropperState.popupClosed = false;
}

// Handle eyedropper error
function handleEyedropperError(message) {
  chrome.storage.local.set({
    eyedropperError: message,
    eyedropperResult: 'error'
  }, () => {
    chrome.action.openPopup();
  });

  eyedropperState.active = false;
  eyedropperState.popupClosed = false;
}

// Handle eyedropper cancellation
function handleEyedropperCancelled() {
  chrome.storage.local.set({
    eyedropperResult: 'cancelled'
  }, () => {
    chrome.action.openPopup();
  });

  eyedropperState.active = false;
  eyedropperState.popupClosed = false;
}

// Listen for extension installation or update
chrome.runtime.onInstalled.addListener(() => {
  // Initialize storage with default values
  chrome.storage.local.get(['savedColors', 'isDarkMode'], (data) => {
    if (!data.savedColors) {
      chrome.storage.local.set({ savedColors: [] });
    }

    // Set default theme if not already set
    if (data.isDarkMode === undefined) {
      chrome.storage.local.set({ isDarkMode: false });
    }
  });

  // Create context menu for color picker
  chrome.contextMenus.create({
    id: "pickColorFromPage",
    title: "🥷 Pick Color with ColorNinja",
    contexts: ["all"]
  });

  console.log('ColorNinja extension installed/updated.');
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "pickColorFromPage") {
    // Inject a script to pick the color from the clicked element
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: getColorFromClickedElement
    }).then(results => {
      if (results && results[0].result) {
        // Add the picked color to storage
        const color = results[0].result;

        chrome.storage.local.get('savedColors', (data) => {
          let savedColors = data.savedColors || [];

          // Check if color already exists
          const existingIndex = savedColors.findIndex(item => item.color === color);

          if (existingIndex >= 0) {
            // Increment count if color already exists
            savedColors[existingIndex].count++;
          } else {
            // Add new color
            savedColors.push({ color, count: 1 });
          }

          // Sort saved colors by count (descending)
          savedColors.sort((a, b) => b.count - a.count);

          // Save to storage
          chrome.storage.local.set({ savedColors });

          // Copy color to clipboard and show notification
          copyToClipboard(color, tab.id);
        });
      }
    });
  }
});

// Clean up old tab-specific color data periodically
chrome.runtime.onStartup.addListener(() => {
  cleanupOldTabData();
});

// Clean up old tab-specific data
function cleanupOldTabData() {
  chrome.storage.local.get(null, (data) => {
    const keysToRemove = [];
    const currentTime = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    for (const key in data) {
      if (key.startsWith('extractedColors_')) {
        // Check if tab still exists or if data is old
        const tabId = parseInt(key.replace('extractedColors_', ''));
        chrome.tabs.get(tabId, (tab) => {
          if (chrome.runtime.lastError || !tab) {
            // Tab doesn't exist anymore, mark for removal
            keysToRemove.push(key);
          }
        });
      }
    }

    if (keysToRemove.length > 0) {
      chrome.storage.local.remove(keysToRemove);
      console.log('Cleaned up old tab data:', keysToRemove);
    }
  });
}

// Function to copy text to clipboard
function copyToClipboard(text, tabId) {
  // Create a temporary notification
  chrome.scripting.executeScript({
    target: { tabId },
    func: (text) => {
      // Show a tooltip/notification
      const tooltip = document.createElement('div');
      tooltip.textContent = `🥷 ColorNinja: Copied ${text}`;
      tooltip.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #6f42c1, #9b59b6);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        z-index: 999999;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 4px 20px rgba(111, 66, 193, 0.3);
        animation: slideInDown 0.3s ease-out;
      `;

      // Add animation keyframes
      if (!document.querySelector('#colorNinjaStyles')) {
        const style = document.createElement('style');
        style.id = 'colorNinjaStyles';
        style.textContent = `
          @keyframes slideInDown {
            0% { transform: translateX(-50%) translateY(-100px); opacity: 0; }
            100% { transform: translateX(-50%) translateY(0); opacity: 1; }
          }
          @keyframes slideOutUp {
            0% { transform: translateX(-50%) translateY(0); opacity: 1; }
            100% { transform: translateX(-50%) translateY(-100px); opacity: 0; }
          }
        `;
        document.head.appendChild(style);
      }

      document.body.appendChild(tooltip);

      // Remove the tooltip after 3 seconds
      setTimeout(() => {
        tooltip.style.animation = 'slideOutUp 0.3s ease-in';
        setTimeout(() => {
          if (tooltip.parentNode) {
            tooltip.parentNode.removeChild(tooltip);
          }
        }, 300);
      }, 2500);

      // Copy to clipboard
      navigator.clipboard.writeText(text).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      });
    },
    args: [text]
  });
}

// Function to get color from clicked element
function getColorFromClickedElement() {
  // We need to store the most recent clicked element
  // This is a bit hacky, but necessary since the context menu click
  // doesn't give us direct access to the element that was clicked
  if (!window._lastClickedElement) {
    // Set up a click tracker if it doesn't exist
    window.addEventListener('mousedown', function(e) {
      window._lastClickedElement = e.target;
    }, true);

    // We don't have a clicked element yet
    return null;
  }

  const el = window._lastClickedElement;
  const styles = window.getComputedStyle(el);

  // Convert RGB to hex
  function rgbToHex(rgb) {
    if (!rgb || rgb === 'transparent' || rgb === 'rgba(0, 0, 0, 0)') {
      return null;
    }

    const match = rgb.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/i);
    if (!match) return null;

    const r = parseInt(match[1]);
    const g = parseInt(match[2]);
    const b = parseInt(match[3]);

    // Convert to hex and ensure 2 digits
    const toHex = (c) => {
      const hex = c.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  // Try to get the most relevant color from the element
  let color = rgbToHex(styles.backgroundColor);

  if (!color || color === '#ffffff' || color === '#000000') {
    color = rgbToHex(styles.color);
  }

  if (!color || color === '#ffffff' || color === '#000000') {
    color = rgbToHex(styles.borderColor);
  }

  return color;
}

// Handle tab removal to clean up storage
chrome.tabs.onRemoved.addListener((tabId) => {
  const storageKey = `extractedColors_${tabId}`;
  chrome.storage.local.remove([storageKey]);
});