// DOM Elements
const extractBtn = document.getElementById('extract-colors');
const downloadBtn = document.getElementById('download-json');
const colorPicker = document.getElementById('color-picker');
const savePickedColorBtn = document.getElementById('save-picked-color');
const startEyedropperBtn = document.getElementById('start-eyedropper');
const cancelEyedropperBtn = document.getElementById('cancel-eyedropper');
const themeToggle = document.getElementById('theme-toggle');
const colorsContainer = document.getElementById('colors-container');
const savedColorsContainer = document.getElementById('saved-colors-container');
const loader = document.getElementById('loader');
const noColorsMsg = document.getElementById('no-colors');
const noSavedColorsMsg = document.getElementById('no-saved-colors');
const messageContainer = document.getElementById('message-container');
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const minimizedPopup = document.getElementById('minimized-popup');
const mainContainer = document.getElementById('main-container');

// Global variables
let extractedColors = [];
let savedColors = [];
let currentTabId = null;
let eyedropperActive = false;

// Initialize the extension
document.addEventListener('DOMContentLoaded', () => {
  // Get current tab ID
  getCurrentTabId().then(tabId => {
    currentTabId = tabId;
    loadTabSpecificColors(tabId);
    loadSavedColors();

    // Show popup immediately once data is loaded
    document.body.classList.add('loaded');
  });

  // Load theme setting
  chrome.storage.local.get('isDarkMode', (data) => {
    if (data.isDarkMode) {
      document.body.classList.add('dark-theme');
      themeToggle.checked = true;
    }
  });

  // Set event listeners
  extractBtn.addEventListener('click', extractColorsFromPage);
  downloadBtn.addEventListener('click', downloadAsJSON);
  savePickedColorBtn.addEventListener('click', savePickedColor);
  startEyedropperBtn.addEventListener('click', startEyedropper);
  cancelEyedropperBtn.addEventListener('click', cancelEyedropper);
  colorPicker.addEventListener('change', () => {
    // Auto copy color to clipboard when picked
    copyToClipboard(colorPicker.value);
  });
  themeToggle.addEventListener('change', toggleTheme);

  // Tab switching
  tabBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const tabId = btn.dataset.tab;
      tabBtns.forEach(b => b.classList.remove('active'));
      tabContents.forEach(c => c.classList.remove('active'));
      btn.classList.add('active');
      document.getElementById(tabId).classList.add('active');
    });
  });
});

// Get current tab ID
async function getCurrentTabId() {
  return new Promise((resolve) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      resolve(tabs[0].id);
    });
  });
}

// Load tab-specific colors
function loadTabSpecificColors(tabId) {
  const storageKey = `extractedColors_${tabId}`;
  chrome.storage.local.get(storageKey, (data) => {
    if (data[storageKey] && data[storageKey].length > 0) {
      extractedColors = data[storageKey];
      renderColors();
    }
  });
}

// Save tab-specific colors
function saveTabSpecificColors(tabId, colors) {
  const storageKey = `extractedColors_${tabId}`;
  chrome.storage.local.set({ [storageKey]: colors });
}

// Load saved colors
function loadSavedColors() {
  chrome.storage.local.get('savedColors', (data) => {
    if (data.savedColors && data.savedColors.length > 0) {
      savedColors = data.savedColors;
      renderSavedColors();
    }
  });
}

// Start eyedropper functionality
async function startEyedropper() {
  if (!window.EyeDropper) {
    showMessage('EyeDropper API not supported in this browser', 'error');
    return;
  }

  try {
    eyedropperActive = true;
    showMinimizedView();

    const eyeDropper = new EyeDropper();
    const result = await eyeDropper.open();

    if (result.sRGBHex) {
      colorPicker.value = result.sRGBHex;
      copyToClipboard(result.sRGBHex);
      showMessage(`Color picked: ${result.sRGBHex}`, 'success');
    }
  } catch (err) {
    if (err.name !== 'AbortError') {
      showMessage('Error using eyedropper: ' + err.message, 'error');
    }
  } finally {
    eyedropperActive = false;
    hideMinimizedView();
  }
}

// Cancel eyedropper
function cancelEyedropper() {
  eyedropperActive = false;
  hideMinimizedView();
}

// Show minimized view
function showMinimizedView() {
  minimizedPopup.style.display = 'flex';
  mainContainer.style.display = 'none';
}

// Hide minimized view
function hideMinimizedView() {
  minimizedPopup.style.display = 'none';
  mainContainer.style.display = 'block';
}

// Extract colors from the current page
function extractColorsFromPage() {
  showLoader();
  hideNoColorsMessage();

  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    chrome.tabs.sendMessage(tabs[0].id, { action: "extractColors" }, (response) => {
      if (chrome.runtime.lastError) {
        // Handle error - content script might not be injected yet
        chrome.scripting.executeScript({
          target: { tabId: tabs[0].id },
          files: ['content.js']
        }, () => {
          // Retry after injection
          setTimeout(() => {
            chrome.tabs.sendMessage(tabs[0].id, { action: "extractColors" }, handleColorsResponse);
          }, 200);
        });
      } else {
        handleColorsResponse(response);
      }
    });
  });
}

// Handle the response from content script
function handleColorsResponse(colors) {
  hideLoader();

  if (!colors || colors.length === 0) {
    showNoColorsMessage();
    return;
  }

  extractedColors = colors;
  // Save tab-specific colors
  if (currentTabId) {
    saveTabSpecificColors(currentTabId, colors);
  }
  renderColors();
}

// Render extracted colors
function renderColors() {
  colorsContainer.innerHTML = '';

  if (extractedColors.length === 0) {
    showNoColorsMessage();
    return;
  }

  hideNoColorsMessage();

  extractedColors.forEach(colorData => {
    const { color, count } = colorData;
    const colorCard = createColorCard(color, count, false);
    colorsContainer.appendChild(colorCard);
  });
}

// Render saved colors
function renderSavedColors() {
  savedColorsContainer.innerHTML = '';

  if (savedColors.length === 0) {
    noSavedColorsMsg.style.display = 'block';
    return;
  }

  noSavedColorsMsg.style.display = 'none';

  savedColors.forEach(colorData => {
    const { color, count } = colorData;
    const colorCard = createColorCard(color, count, true);
    savedColorsContainer.appendChild(colorCard);
  });
}

// Create a color card element
function createColorCard(color, count, isSaved = false) {
  const colorCard = document.createElement('div');
  colorCard.className = isSaved ? 'color-card saved-color' : 'color-card';

  // Calculate contrasting text color
  const textColor = getContrastColor(color);

  // Create remove button for saved colors
  if (isSaved) {
    const removeBtn = document.createElement('button');
    removeBtn.className = 'remove-color';
    removeBtn.innerHTML = '×';
    removeBtn.title = 'Remove color';
    removeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      removeSavedColor(color);
    });
    colorCard.appendChild(removeBtn);
  }

  // Create color preview area
  const colorPreview = document.createElement('div');
  colorPreview.className = 'color-preview';
  colorPreview.style.backgroundColor = color;
  colorPreview.textContent = color;
  colorPreview.style.color = textColor;
  colorPreview.title = 'Click to copy HEX code';
  colorPreview.addEventListener('click', () => {
    copyToClipboard(color);
  });

  // Create color info area
  const colorInfo = document.createElement('div');
  colorInfo.className = 'color-info';

  // Add count
  const countElement = document.createElement('div');
  countElement.className = 'color-count';
  countElement.textContent = `Used ${count} ${count === 1 ? 'time' : 'times'}`;

  // Add format buttons
  const formatDiv = document.createElement('div');
  formatDiv.className = 'color-format';

  // HEX format button
  const hexBtn = document.createElement('button');
  hexBtn.className = 'format-btn';
  hexBtn.textContent = 'HEX';
  hexBtn.addEventListener('click', () => {
    copyToClipboard(color);
  });

  // RGB format button
  const rgbBtn = document.createElement('button');
  rgbBtn.className = 'format-btn';
  rgbBtn.textContent = 'RGB';
  rgbBtn.addEventListener('click', () => {
    copyToClipboard(hexToRgb(color));
  });

  // HSL format button
  const hslBtn = document.createElement('button');
  hslBtn.className = 'format-btn';
  hslBtn.textContent = 'HSL';
  hslBtn.addEventListener('click', () => {
    copyToClipboard(hexToHsl(color));
  });

  formatDiv.appendChild(hexBtn);
  formatDiv.appendChild(rgbBtn);
  formatDiv.appendChild(hslBtn);

  colorInfo.appendChild(countElement);
  colorInfo.appendChild(formatDiv);

  // Add save button for extracted colors
  if (!isSaved) {
    const saveBtn = document.createElement('button');
    saveBtn.className = 'save-color-btn';
    saveBtn.innerHTML = '💾 Save';
    saveBtn.addEventListener('click', () => {
      saveColor(color);
    });
    colorInfo.appendChild(saveBtn);
  }

  colorCard.appendChild(colorPreview);
  colorCard.appendChild(colorInfo);

  return colorCard;
}

// Save a color to saved colors
function saveColor(color) {
  // Check if color already exists
  const existingIndex = savedColors.findIndex(item => item.color.toLowerCase() === color.toLowerCase());

  if (existingIndex >= 0) {
    // Increment count if color already exists
    savedColors[existingIndex].count++;
  } else {
    // Add new color
    savedColors.push({ color, count: 1 });
  }

  // Sort saved colors by count (descending)
  savedColors.sort((a, b) => b.count - a.count);

  // Save to storage
  chrome.storage.local.set({ savedColors });

  // Render saved colors
  renderSavedColors();

  // Show message
  showMessage('Color saved!', 'success');
}

// Remove a saved color
function removeSavedColor(color) {
  savedColors = savedColors.filter(item => item.color.toLowerCase() !== color.toLowerCase());

  // Save to storage
  chrome.storage.local.set({ savedColors });

  // Render saved colors
  renderSavedColors();

  // Show message
  showMessage('Color removed!', 'success');
}

// Save picked color
function savePickedColor() {
  const color = colorPicker.value;
  saveColor(color);
  copyToClipboard(color);
}

// Download colors as JSON
function downloadAsJSON() {
  // Combine extracted and saved colors
  const allColors = {
    extractedColors,
    savedColors,
    timestamp: new Date().toISOString(),
    tabId: currentTabId
  };

  // Create blob and download link
  const dataStr = JSON.stringify(allColors, null, 2);
  const blob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = `colorninja-colors-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showMessage('JSON file downloaded!', 'success');
}

// Toggle theme
function toggleTheme() {
  const isDarkMode = themeToggle.checked;
  if (isDarkMode) {
    document.body.classList.add('dark-theme');
  } else {
    document.body.classList.remove('dark-theme');
  }

  // Save theme setting
  chrome.storage.local.set({ isDarkMode });
}

// Utility Functions

// Copy to clipboard and show message
function copyToClipboard(text) {
  navigator.clipboard.writeText(text)
    .then(() => {
      showMessage('Copied: ' + text, 'success');
    })
    .catch(err => {
      console.error('Failed to copy: ', err);
      showMessage('Failed to copy color', 'error');
    });
}

// Convert HEX to RGB
function hexToRgb(hex) {
  // Remove # if present
  hex = hex.replace('#', '');

  // Convert shorthand hex to full form
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgb(${r}, ${g}, ${b})`;
}

// Convert HEX to HSL
function hexToHsl(hex) {
  // Remove # if present
  hex = hex.replace('#', '');

  // Convert shorthand hex to full form
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Convert hex to RGB
  let r = parseInt(hex.substring(0, 2), 16) / 255;
  let g = parseInt(hex.substring(2, 4), 16) / 255;
  let b = parseInt(hex.substring(4, 6), 16) / 255;

  // Find max and min RGB values
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;

  if (max === min) {
    // Achromatic
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }

    h = Math.round(h * 60);
  }

  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return `hsl(${h}, ${s}%, ${l}%)`;
}

// Get contrasting text color (black or white)
function getContrastColor(hexColor) {
  // Remove # if present
  hexColor = hexColor.replace('#', '');

  // Convert shorthand hex to full form
  if (hexColor.length === 3) {
    hexColor = hexColor[0] + hexColor[0] + hexColor[1] + hexColor[1] + hexColor[2] + hexColor[2];
  }

  // Convert hex to RGB
  const r = parseInt(hexColor.substring(0, 2), 16);
  const g = parseInt(hexColor.substring(2, 4), 16);
  const b = parseInt(hexColor.substring(4, 6), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return white for dark colors, black for light colors
  return luminance > 0.5 ? '#000000' : '#ffffff';
}

// Show loader
function showLoader() {
  loader.style.display = 'block';
}

// Hide loader
function hideLoader() {
  loader.style.display = 'none';
}

// Show no colors message
function showNoColorsMessage() {
  noColorsMsg.style.display = 'block';
}

// Hide no colors message
function hideNoColorsMessage() {
  noColorsMsg.style.display = 'none';
}

// Show message
function showMessage(text, type) {
  messageContainer.textContent = text;
  messageContainer.className = `message-container ${type}`;
  messageContainer.style.display = 'block';

  // Hide message after 3 seconds
  setTimeout(() => {
    messageContainer.style.display = 'none';
  }, 3000);
}