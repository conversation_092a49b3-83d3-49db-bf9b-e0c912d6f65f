<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ColorNinja</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <header>
    <div class="header-left">
      <div class="ninja-icon">🥷</div>
      <h1>ColorNinja</h1>
    </div>
    <div class="mode-toggle">
      <span class="light-icon">☀️</span>
      <label class="switch">
        <input type="checkbox" id="theme-toggle">
        <span class="slider round"></span>
      </label>
      <span class="dark-icon">🌙</span>
    </div>
  </header>

  <!-- Minimized mode for eyedropper -->
  <div class="minimized-popup" id="minimized-popup" style="display: none;">
    <div class="mini-content">
      <span class="ninja-mini">🥷</span>
      <span class="mini-text">Picking color...</span>
      <button id="cancel-eyedropper" class="cancel-btn">Cancel</button>
    </div>
  </div>

  <div class="main-container" id="main-container">
    <div class="controls">
      <button id="extract-colors" class="btn primary">🎨 Extract Colors</button>
      <button id="download-json" class="btn secondary">📥 Download JSON</button>
      <div class="color-picker-container">
        <label for="color-picker">Pick a color:</label>
        <input type="color" id="color-picker" value="#3498db">
        <button id="start-eyedropper" class="btn eyedropper" title="Use eyedropper to pick color from page">👁️</button>
        <button id="save-picked-color" class="btn small">💾 Save</button>
      </div>
    </div>

    <div class="message-container" id="message-container"></div>

    <div class="tabs">
      <button class="tab-btn active" data-tab="extracted-colors">Extracted Colors</button>
      <button class="tab-btn" data-tab="saved-colors">Saved Colors</button>
    </div>

    <div class="tab-content active" id="extracted-colors">
      <div class="colors-container" id="colors-container">
        <div class="loader" id="loader">Loading colors...</div>
        <div class="no-colors" id="no-colors">No colors found. Click "Extract Colors" button to analyze the current page.</div>
      </div>
    </div>

    <div class="tab-content" id="saved-colors">
      <div class="colors-container" id="saved-colors-container">
        <div class="no-colors" id="no-saved-colors">No saved colors yet. Use the color picker or save colors from extracted colors.</div>
      </div>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <p>Made with <span class="heartbeat">❤️</span> by Deepak Raj</p>
      <a href="https://www.linkedin.com/in/deepak-rajj/" target="_blank" class="linkedin-icon" title="Visit LinkedIn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      </a>
    </div>
  </footer>

  <script src="popup.js"></script>
</body>
</html>