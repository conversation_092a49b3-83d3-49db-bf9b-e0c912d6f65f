/* Variables */
:root {
  --primary-color: #6f42c1;
  --secondary-color: #28a745;
  --accent-color: #dc3545;
  --text-color: #333;
  --text-secondary: #666;
  --bg-color: #f8f9fa;
  --card-bg: #fff;
  --border-color: #dee2e6;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --hover-color: #f1f3f4;
  --transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
  --ninja-gradient: linear-gradient(135deg, #6f42c1, #9b59b6);
}

/* Dark Theme */
body.dark-theme {
  --primary-color: #8b5cf6;
  --secondary-color: #10b981;
  --accent-color: #ef4444;
  --text-color: #e5e7eb;
  --text-secondary: #9ca3af;
  --bg-color: #1f2937;
  --card-bg: #374151;
  --border-color: #4b5563;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --hover-color: #4b5563;
  --ninja-gradient: linear-gradient(135deg, #8b5cf6, #a855f7);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 420px;
  height: 580px;
  overflow-y: auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  position: relative;
  opacity: 0;
  visibility: hidden;
}

body.loaded {
  opacity: 1;
  visibility: visible;
}

/* Minimized popup for eyedropper */
.minimized-popup {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--ninja-gradient);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mini-content {
  text-align: center;
  color: white;
  padding: 20px;
}

.ninja-mini {
  font-size: 2rem;
  display: block;
  margin-bottom: 10px;
  animation: pulse 2s infinite;
}

.mini-text {
  display: block;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 500;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  transition: var(--transition);
}

.cancel-btn:hover {
  background: white;
  color: var(--primary-color);
}

header {
  background: var(--ninja-gradient);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px var(--shadow-color);
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ninja-icon {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

h1 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.main-container {
  padding: 20px;
}

/* Controls */
.controls {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Buttons */
.btn {
  cursor: pointer;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  font-weight: 600;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  box-shadow: 0 2px 6px var(--shadow-color);
}

.primary {
  background: var(--ninja-gradient);
  color: white;
}

.secondary {
  background-color: var(--secondary-color);
  color: white;
}

.small {
  padding: 6px 12px;
  font-size: 0.85rem;
}

.eyedropper {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 0.85rem;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.btn:active {
  transform: translateY(0);
}

/* Color Picker */
.color-picker-container {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  background-color: var(--card-bg);
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.color-picker-container label {
  font-weight: 600;
  color: var(--text-secondary);
}

#color-picker {
  width: 50px;
  height: 35px;
  cursor: pointer;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 6px var(--shadow-color);
}

/* Theme Toggle */
.mode-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: rgba(255, 255, 255, 0.3);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 20px;
  background-color: var(--card-bg);
  border-radius: 10px 10px 0 0;
  overflow: hidden;
}

.tab-btn {
  background-color: transparent;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  color: var(--text-secondary);
  font-weight: 600;
  transition: var(--transition);
  flex: 1;
}

.tab-btn.active {
  color: var(--primary-color);
  background-color: var(--hover-color);
  border-bottom: 3px solid var(--primary-color);
}

.tab-btn:hover:not(.active) {
  background-color: var(--hover-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Colors Container */
.colors-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  max-height: 320px;
  overflow-y: auto;
  padding-right: 5px;
}

/* Color Card */
.color-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color);
  overflow: hidden;
  transition: var(--transition);
  position: relative;
}

.color-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px var(--shadow-color);
}

.color-card.saved-color {
  position: relative;
}

.remove-color {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: var(--transition);
  z-index: 10;
}

.remove-color:hover {
  background-color: var(--accent-color);
  transform: scale(1.1);
}

.color-preview {
  height: 80px;
  width: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: transparent;
  transition: var(--transition);
  font-weight: bold;
  font-size: 0.85rem;
}

.color-preview:hover {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.color-info {
  padding: 12px;
}

.color-count {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 8px;
  font-weight: 500;
}

.color-format {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.format-btn {
  font-size: 0.7rem;
  padding: 4px 8px;
  background-color: var(--hover-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-color);
  transition: var(--transition);
  flex: 1;
  text-align: center;
}

.format-btn:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.save-color-btn {
  background: var(--ninja-gradient);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 8px;
  width: 100%;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.save-color-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px var(--shadow-color);
}

/* Message */
.message-container {
  margin: 15px 0;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  display: none;
  font-weight: 500;
}

.message-container.success {
  background-color: rgba(40, 167, 69, 0.15);
  border: 1px solid rgba(40, 167, 69, 0.3);
  color: var(--secondary-color);
}

.message-container.error {
  background-color: rgba(220, 53, 69, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: var(--accent-color);
}

/* Loader */
.loader {
  grid-column: span 3;
  text-align: center;
  padding: 30px;
  color: var(--text-secondary);
  display: none;
  font-size: 1.1rem;
}

.loader::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

/* No Colors */
.no-colors {
  grid-column: span 3;
  text-align: center;
  padding: 30px;
  color: var(--text-secondary);
  font-style: italic;
  font-size: 0.95rem;
}

/* Footer */
footer {
  background-color: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: 15px;
  margin-top: 20px;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.heartbeat {
  color: #e74c3c;
  animation: heartbeat 1.5s ease-in-out infinite;
}

.linkedin-icon {
  color: #0077b5;
  transition: var(--transition);
  padding: 5px;
  border-radius: 4px;
}

.linkedin-icon:hover {
  color: #005885;
  background-color: rgba(0, 119, 181, 0.1);
  transform: scale(1.1);
}

/* Animations */
@keyframes heartbeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 100vw;
  }

  .color-picker-container {
    flex-direction: column;
    align-items: stretch;
  }

  .colors-container {
    grid-template-columns: repeat(2, 1fr);
  }
}