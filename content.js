// This script runs in the context of web pages
// Listen for messages from the popup or background script

let eyedropperIndicator = null;

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script received message:', message);

  if (message.action === "extractColors") {
    const colors = extractColorsFromPage();
    sendResponse(colors);
  } else if (message.action === "startEyedropper") {
    console.log('Starting eyedropper in content script');
    startEyedropperInPage();
    sendResponse({ success: true });
  } else if (message.action === "cancelEyedropper") {
    console.log('Cancelling eyedropper in content script');
    cancelEyedropperInPage();
    sendResponse({ success: true });
  }
  return true; // Indicates we will respond asynchronously
});

// Extract all colors used in the current page
function extractColorsFromPage() {
  const colorMap = new Map();
  const elements = document.querySelectorAll('*');

  elements.forEach(el => {
    const styles = window.getComputedStyle(el);

    // Check background-color
    extractColor(styles.backgroundColor);

    // Check color (text)
    extractColor(styles.color);

    // Check border-color
    extractColor(styles.borderColor);

    // Check box-shadow
    const boxShadow = styles.boxShadow;
    if (boxShadow && boxShadow !== 'none') {
      const colorMatches = boxShadow.match(/rgba?\([^)]+\)|#[0-9a-f]{3,8}/gi);
      if (colorMatches) {
        colorMatches.forEach(color => extractColor(color));
      }
    }

    // Check text-shadow
    const textShadow = styles.textShadow;
    if (textShadow && textShadow !== 'none') {
      const colorMatches = textShadow.match(/rgba?\([^)]+\)|#[0-9a-f]{3,8}/gi);
      if (colorMatches) {
        colorMatches.forEach(color => extractColor(color));
      }
    }

    // Check if element has background-image with gradients
    const bgImage = styles.backgroundImage;
    if (bgImage && bgImage.includes('gradient')) {
      const colorMatches = bgImage.match(/rgba?\([^)]+\)|#[0-9a-f]{3,8}/gi);
      if (colorMatches) {
        colorMatches.forEach(color => extractColor(color));
      }
    }

    // Check outline-color
    extractColor(styles.outlineColor);

    // Check border-top/right/bottom/left-color
    extractColor(styles.borderTopColor);
    extractColor(styles.borderRightColor);
    extractColor(styles.borderBottomColor);
    extractColor(styles.borderLeftColor);
  });

  // Extract color and add to map
  function extractColor(color) {
    if (!color || color === 'transparent' || color === 'rgba(0, 0, 0, 0)' || color === 'none' || color === 'initial' || color === 'inherit') {
      return;
    }

    let hexColor;

    // Handle different color formats
    if (color.startsWith('#')) {
      hexColor = standardizeHex(color);
    } else if (color.startsWith('rgb')) {
      hexColor = rgbToHex(color);
    } else if (color.startsWith('hsl')) {
      hexColor = hslToHex(color);
    } else {
      // Try to handle named colors by creating a temporary element
      try {
        const tempEl = document.createElement('div');
        tempEl.style.color = color;
        document.body.appendChild(tempEl);
        const computedColor = window.getComputedStyle(tempEl).color;
        document.body.removeChild(tempEl);
        if (computedColor && computedColor.startsWith('rgb')) {
          hexColor = rgbToHex(computedColor);
        }
      } catch (e) {
        // If named color conversion fails, skip it
        return;
      }
    }

    if (hexColor && hexColor !== '#000000' && hexColor !== '#ffffff') {
      colorMap.set(hexColor, (colorMap.get(hexColor) || 0) + 1);
    }
  }

  // Standardize hex colors (e.g., #abc to #aabbcc)
  function standardizeHex(hex) {
    // Convert shorthand hex (#abc) to full form (#aabbcc)
    if (hex.length === 4) {
      hex = '#' + hex[1] + hex[1] + hex[2] + hex[2] + hex[3] + hex[3];
    }
    return hex.toLowerCase();
  }

  // Convert RGB(A) to hex
  function rgbToHex(rgb) {
    // Extract the numbers from the rgba/rgb string
    const match = rgb.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/i);
    if (!match) return null;

    const r = parseInt(match[1]);
    const g = parseInt(match[2]);
    const b = parseInt(match[3]);
    const a = match[4] ? parseFloat(match[4]) : 1;

    // Skip transparent colors
    if (a < 0.1) return null;

    // Convert to hex and ensure 2 digits
    const toHex = (c) => {
      const hex = c.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  // Convert HSL to hex
  function hslToHex(hsl) {
    const match = hsl.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/i);
    if (!match) return null;

    let h = parseInt(match[1]) / 360;
    let s = parseInt(match[2]) / 100;
    let l = parseInt(match[3]) / 100;

    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    const toHex = (c) => {
      const hex = Math.round(c * 255).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  // Sort colors by frequency (descending) and filter out very common colors
  return Array.from(colorMap.entries())
    .filter(([color, count]) => {
      // Filter out very common background/text colors that might not be meaningful
      const commonColors = ['#ffffff', '#000000', '#f8f8f8', '#f5f5f5'];
      return !commonColors.includes(color.toLowerCase());
    })
    .sort((a, b) => b[1] - a[1])
    .slice(0, 50) // Limit to top 50 colors
    .map(([color, count]) => ({ color, count }));
}

// Eyedropper functionality in webpage context
async function startEyedropperInPage() {
  console.log('startEyedropperInPage called');

  if (!window.EyeDropper) {
    console.log('EyeDropper API not supported');
    chrome.runtime.sendMessage({
      action: 'eyedropperError',
      message: 'EyeDropper API not supported in this browser'
    });
    return;
  }

  try {
    console.log('Showing eyedropper indicator');
    // Show floating indicator
    showEyedropperIndicator();

    console.log('Creating EyeDropper instance');
    const eyeDropper = new EyeDropper();
    const result = await eyeDropper.open();
    console.log('EyeDropper result:', result);

    if (result.sRGBHex) {
      console.log('Sending color result to popup:', result.sRGBHex);
      // Send color back to popup
      chrome.runtime.sendMessage({
        action: 'eyedropperResult',
        color: result.sRGBHex
      });
    }
  } catch (err) {
    console.error('Error in eyedropper:', err);
    if (err.name !== 'AbortError') {
      chrome.runtime.sendMessage({
        action: 'eyedropperError',
        message: 'Error using eyedropper: ' + err.message
      });
    }
  } finally {
    console.log('Hiding eyedropper indicator');
    hideEyedropperIndicator();
  }
}

// Cancel eyedropper
function cancelEyedropperInPage() {
  hideEyedropperIndicator();
  chrome.runtime.sendMessage({
    action: 'eyedropperCancelled'
  });
}

// Show floating eyedropper indicator
function showEyedropperIndicator() {
  if (eyedropperIndicator) return;

  eyedropperIndicator = document.createElement('div');
  eyedropperIndicator.id = 'ninja-eyedropper-indicator';
  eyedropperIndicator.innerHTML = `
    <div style="
      position: fixed;
      top: 10px;
      right: 10px;
      width: 200px;
      height: 80px;
      background: linear-gradient(135deg, #6f42c1, #9b59b6);
      z-index: 2147483647;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: white;
      pointer-events: auto;
    ">
      <div style="
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
      ">
        <span style="
          font-size: 1.5rem;
          animation: ninja-pulse 2s infinite;
        ">🥷</span>
        <span style="
          font-size: 1rem;
          font-weight: 500;
        ">Picking color...</span>
        <button onclick="window.cancelNinjaEyedropper()" style="
          background: rgba(255, 255, 255, 0.2);
          border: 2px solid white;
          color: white;
          padding: 6px 12px;
          border-radius: 15px;
          cursor: pointer;
          font-weight: bold;
          font-size: 0.85rem;
          transition: all 0.3s ease;
        ">Cancel</button>
      </div>
    </div>
  `;

  // Add animation styles
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ninja-pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
  `;
  document.head.appendChild(style);

  // Add global cancel function
  window.cancelNinjaEyedropper = cancelEyedropperInPage;

  document.body.appendChild(eyedropperIndicator);
}

// Hide eyedropper indicator
function hideEyedropperIndicator() {
  if (eyedropperIndicator) {
    eyedropperIndicator.remove();
    eyedropperIndicator = null;
  }

  // Remove global function
  if (window.cancelNinjaEyedropper) {
    delete window.cancelNinjaEyedropper;
  }

  // Remove animation styles
  const styles = document.querySelectorAll('style');
  styles.forEach(style => {
    if (style.textContent.includes('ninja-pulse')) {
      style.remove();
    }
  });
}