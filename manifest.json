{"manifest_version": 3, "name": "ColorNinja", "version": "2.0", "description": "Advanced color extraction and management tool with tab-specific storage", "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "permissions": ["activeTab", "scripting", "storage", "contextMenus"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content.js"]}]}